"""
Production settings for US Limo Group Management System
"""

from .settings import *

# Production settings
DEBUG = False

# Add your domain here
ALLOWED_HOSTS = [
    'yourusername.pythonanywhere.com',  # Replace with your PythonAnywhere username
    'uslimogroup.com',
    'www.uslimogroup.com',
    '127.0.0.1',
    'localhost'
]

# Database for production (SQLite is fine for small businesses)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_production.sqlite3',
    }
}

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Security settings for production
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'SAMEORIGIN'  # Allow iframe embedding for WordPress

# Email settings (configure with your email provider)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # Your email
EMAIL_HOST_PASSWORD = 'your-app-password'  # Your email app password

# Default from email
DEFAULT_FROM_EMAIL = 'US Limo Group <<EMAIL>>'
SERVER_EMAIL = '<EMAIL>'

# CORS settings for API access
CORS_ALLOWED_ORIGINS = [
    "https://uslimogroup.com",
    "https://www.uslimogroup.com",
    "https://yourusername.pythonanywhere.com",  # Replace with your username
]

# Session settings
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
