#!/usr/bin/env python
"""
Quick deployment script for PythonAnywhere
This will create all necessary files directly on the server
"""

import os
import subprocess

def create_project_structure():
    """Create the basic project structure"""
    
    # Create directories
    directories = [
        'limo_management',
        'accounts',
        'bookings', 
        'fleet',
        'payments',
        'templates',
        'templates/base',
        'templates/accounts',
        'templates/bookings',
        'templates/fleet',
        'static',
        'media'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    print("\n🎉 Project structure created!")
    print("📁 Directories ready for file upload")
    
    # Create requirements file
    with open('requirements.txt', 'w') as f:
        f.write("""Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
python-decouple==3.8
""")
    
    print("✓ Created requirements.txt")
    
    # Create basic manage.py
    with open('manage.py', 'w') as f:
        f.write("""#!/usr/bin/env python
import os
import sys

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'limo_management.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)
""")
    
    print("✓ Created manage.py")
    print("\n🚀 Ready for Django setup!")

if __name__ == '__main__':
    create_project_structure()
