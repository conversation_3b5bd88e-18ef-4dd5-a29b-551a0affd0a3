@echo off
echo ========================================
echo   Creating Deployment Package
echo   US Limo Group Management System
echo ========================================
echo.

echo 📦 Creating deployment-ready files...

REM Create production settings
echo Creating production settings...
copy settings_production.py limo_management\settings_production.py

REM Create requirements file
echo Creating requirements.txt...
echo Django==4.2.7 > requirements_production.txt
echo djangorestframework==3.14.0 >> requirements_production.txt
echo django-cors-headers==4.3.1 >> requirements_production.txt
echo python-decouple==3.8 >> requirements_production.txt

echo.
echo ✅ Deployment files ready!
echo.
echo 📋 Next steps:
echo 1. Create ZIP file of entire project folder
echo 2. Upload to PythonAnywhere
echo 3. Configure web app
echo.
echo 📁 Files to include in ZIP:
echo   - All Python files (*.py)
echo   - templates/ folder
echo   - static/ folder
echo   - All app folders (accounts, bookings, fleet, payments)
echo   - manage.py
echo   - requirements_production.txt
echo   - wsgi_pythonanywhere.py
echo.
pause
