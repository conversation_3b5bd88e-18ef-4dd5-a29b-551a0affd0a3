# 🚀 US Limo Group - PythonAnywhere Deployment Guide

## 🎯 **Complete Deployment to PythonAnywhere**

### **Step 1: Create Account**
1. Go to **https://www.pythonanywhere.com/**
2. Click **"Pricing & signup"**
3. Choose **"Create a Beginner account"** (FREE)
4. Sign up with your email

### **Step 2: Upload Your Files**
1. **Download your project as ZIP:**
   - Go to your project folder: `c:\Users\<USER>\Desktop\web`
   - Select all files and create ZIP file
   - Name it: `us-limo-group.zip`

2. **Upload to PythonAnywhere:**
   - Login to PythonAnywhere
   - Go to **"Files"** tab
   - Click **"Upload a file"**
   - Upload your `us-limo-group.zip`
   - Extract the files

### **Step 3: Create Web App**
1. Go to **"Web"** tab
2. Click **"Add a new web app"**
3. Choose **"Manual configuration"**
4. Select **"Python 3.10"**
5. Click **"Next"**

### **Step 4: Configure Django**
1. **Set Source Code:**
   - Source code: `/home/<USER>/us-limo-group/`
   - Working directory: `/home/<USER>/us-limo-group/`

2. **Edit WSGI file:**
   - Click on WSGI configuration file
   - Replace content with our WSGI configuration

3. **Install Requirements:**
   - Open **"Consoles"** tab
   - Start **"Bash console"**
   - Run: `pip3.10 install --user Django djangorestframework django-cors-headers`

### **Step 5: Setup Database**
1. In the console, run:
   ```bash
   cd us-limo-group
   python manage.py migrate
   python setup_complete_system.py
   ```

### **Step 6: Configure Domain**
1. **Free subdomain:** `yourusername.pythonanywhere.com`
2. **Custom domain:** Point `uslimogroup.com` to PythonAnywhere
   - Add CNAME record: `uslimogroup.com` → `yourusername.pythonanywhere.com`

### **Step 7: Enable HTTPS**
- PythonAnywhere provides free SSL certificates
- Automatically enabled for both subdomains and custom domains

## 🎉 **Your System Will Be Live At:**
- **Free URL:** `https://yourusername.pythonanywhere.com`
- **Custom URL:** `https://uslimogroup.com` (after DNS setup)

## 📞 **Login Credentials:**
- **Admin:** admin / admin123
- **Client:** client_sarah / client123
- **Driver:** driver_john / driver123

## 🔗 **WordPress Integration:**
```html
<iframe src="https://uslimogroup.com/reservation/" width="100%" height="700"></iframe>
```

## 💰 **Cost Breakdown:**
- **PythonAnywhere Free:** $0/month (perfect for testing)
- **PythonAnywhere Hacker:** $5/month (for production)
- **Domain:** Free (if you don't have uslimogroup.com yet)
- **SSL:** Free (included)

## 🚀 **Total Monthly Cost: $0-5**

Perfect for a professional limousine business! 🚗✨
