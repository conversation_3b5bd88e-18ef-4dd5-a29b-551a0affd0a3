@echo off
echo ========================================
echo    🚗 US LIMO GROUP MANAGEMENT SYSTEM
echo    Complete Admin Control System
echo ========================================
echo.

echo 📦 Installing core requirements...
pip install Django==4.2.7 djangorestframework==3.14.0 django-cors-headers==4.3.1 python-decouple==3.8

echo.
echo 🗄️ Setting up database...
python manage.py makemigrations
python manage.py migrate

echo.
echo 👥 Creating system data...
python setup_complete_system.py

echo.
echo ========================================
echo    🎉 SYSTEM READY!
echo ========================================
echo.
echo 🌐 SYSTEM URLS:
echo    🏠 Homepage: http://127.0.0.1:8000/
echo    📝 Reservation: http://127.0.0.1:8000/reservation/
echo    ⚙️ Admin Panel: http://127.0.0.1:8000/admin/
echo    🚗 Fleet: http://127.0.0.1:8000/fleet/
echo.
echo 🔐 LOGIN CREDENTIALS:
echo    👑 Admin: admin / admin123
echo    👤 Client: client_sarah / client123
echo    🚗 Driver: driver_john / driver123
echo.
echo 📞 CONTACT INFO:
echo    🌐 Website: uslimogroup.com
echo    📧 Email: <EMAIL>
echo    📱 Phone: (*************
echo.
echo 🔗 WORDPRESS INTEGRATION:
echo    ^<iframe src="http://127.0.0.1:8000/reservation/" width="100%%" height="700"^>^</iframe^>
echo.
echo ⚡ Starting server...
echo    Press Ctrl+C to stop
echo ========================================
echo.

python manage.py runserver 127.0.0.1:8000

echo.
echo 🛑 Server stopped.
pause
