# 🚀 Deploy US Limo Group to PythonAnywhere

## ⚡ **Quick Deployment (30 minutes)**

### **Step 1: Create Account (5 minutes)**
1. Go to **https://www.pythonanywhere.com/**
2. Click **"Pricing & signup"**
3. Choose **"Create a Beginner account"** (FREE)
4. Sign up with your email
5. Verify your email

### **Step 2: Upload Files (10 minutes)**
1. **Create ZIP file:**
   - Go to `c:\Users\<USER>\Desktop\web`
   - Select ALL files and folders
   - Right-click → "Send to" → "Compressed folder"
   - Name it: `us-limo-group.zip`

2. **Upload to PythonAnywhere:**
   - Login to PythonAnywhere dashboard
   - Click **"Files"** tab
   - Click **"Upload a file"**
   - Select your `us-limo-group.zip`
   - After upload, click on the ZIP file
   - Click **"Extract here"**

### **Step 3: Create Web App (5 minutes)**
1. Click **"Web"** tab
2. Click **"Add a new web app"**
3. Choose your domain (free: `yourusername.pythonanywhere.com`)
4. Select **"Manual configuration"**
5. Choose **"Python 3.10"**
6. Click **"Next"**

### **Step 4: Configure App (5 minutes)**
1. **Set paths:**
   - Source code: `/home/<USER>/us-limo-group/`
   - Working directory: `/home/<USER>/us-limo-group/`

2. **Edit WSGI file:**
   - Click on the WSGI configuration file link
   - Delete all content
   - Copy content from `wsgi_pythonanywhere.py`
   - Replace `yourusername` with your actual username
   - Save file

### **Step 5: Install Requirements (3 minutes)**
1. Click **"Consoles"** tab
2. Click **"Bash"**
3. Run these commands:
   ```bash
   pip3.10 install --user Django==4.2.7
   pip3.10 install --user djangorestframework==3.14.0
   pip3.10 install --user django-cors-headers==4.3.1
   ```

### **Step 6: Setup Database (2 minutes)**
1. In the same console:
   ```bash
   cd us-limo-group
   python3.10 manage.py migrate
   python3.10 setup_complete_system.py
   ```

### **Step 7: Go Live! (1 minute)**
1. Go back to **"Web"** tab
2. Click **"Reload yourusername.pythonanywhere.com"**
3. Click on your domain link
4. **Your site is LIVE!** 🎉

## 🌐 **Your Live URLs:**
- **Homepage:** `https://yourusername.pythonanywhere.com/`
- **Reservation:** `https://yourusername.pythonanywhere.com/reservation/`
- **Admin:** `https://yourusername.pythonanywhere.com/admin/`

## 🔐 **Login Credentials:**
- **Admin:** admin / admin123
- **Client:** client_sarah / client123

## 🔗 **Custom Domain (Optional)**
To use `uslimogroup.com`:
1. Upgrade to Hacker plan ($5/month)
2. Add custom domain in Web tab
3. Point your domain DNS to PythonAnywhere

## 💰 **Cost:**
- **Free tier:** Perfect for testing and small business
- **Hacker plan:** $5/month for custom domain
- **SSL:** Free (automatic)

## 🎉 **You're Live!**
Your professional US Limo Group Management System will be live on the internet in 30 minutes! 🚗✨

## 📞 **Need Help?**
If you get stuck on any step, just let me know and I'll help you through it!
